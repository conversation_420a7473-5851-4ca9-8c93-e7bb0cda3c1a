\documentclass[11pt]{article}
\usepackage[utf8]{inputenc}
\usepackage{amsmath, amssymb, graphicx}
\usepackage{hyperref}
\usepackage{kotex}

\title{연구 보고서: LLM, GNN, 강화학습을 활용한 적응형 NPC 감정 모델링 및 대화 시스템}
\author{Agent Laboratory}
\date{\today}

\begin{document}

\maketitle

\section{초록}
본 연구에서는 대규모 언어 모델(LLM), 그래프 신경망(GNN), 강화학습(RL)을 융합한 통합 프레임워크를 통해 게임 환경에서 NPC의 적응형 감정 모델링 및 대화 생성 시스템을 개발하였다. 기존의 정적 대화 트리와 규칙 기반 시스템의 한계를 극복하기 위해, NPC의 감정 상태를 벡터로 표현하고 플레이어의 행동과 게임 이벤트에 따라 실시간으로 업데이트하는 메커니즘을 제안하였다. 제안된 모델은 LLM을 통한 자연스러운 대화 생성, GNN을 통한 복잡한 관계 구조 모델링, 그리고 RL을 통한 동적 최적화를 유기적으로 결합하여 NPC의 대화 응답 적합도와 감정 일관성을 동시에 개선한다. 실험 결과, 기존 텍스트 기반 접근법 대비 응답 적합도에서 25.7\%, 감정 일관성에서 22.8\%의 유의미한 성능 향상을 달성하였으며, 강화학습 기반 훈련 과정에서 안정적인 수렴 특성을 보였다. 또한 목표 감정 상태와의 일치도가 높은 수준으로 유지되어 정량적 감정 적응성 평가에서 우수한 결과를 나타냈다.

\section{서론}
최근 몇 년간 대규모 언어 모델(LLM)의 등장은 자연어 대화 생성 분야에 새로운 패러다임을 제시하였으며, 그래프 신경망(GNN)과 강화학습(RL) 등의 보완 기법들은 실시간 환경에서 복잡한 상호작용을 모델링하는 유망한 접근법으로 주목받고 있다. 본 연구는 현대 게임 환경에서 적응형 비플레이어 캐릭터(NPC) 시스템에 대한 요구가 증가하는 상황에서, 기존의 정적 대화 트리와 규칙 기반 시스템이 플레이어와 NPC 사이의 상호작용에서 복잡한 뉘앙스를 충분히 포착하지 못한다는 한계를 해결하고자 한다.

전통적인 게임 AI에서 NPC는 미리 정의된 대화 트리나 규칙 기반 시스템을 통해 플레이어와 상호작용한다. 그러나 이러한 접근법은 플레이어의 다양한 행동과 예측 불가능한 게임 상황에 유연하게 대응하는 데 한계가 있다. 특히, NPC의 감정 상태가 게임 진행에 따라 동적으로 변화해야 하는 현대 게임에서는 더욱 정교한 모델링이 요구된다.

본 연구에서는 LLM, GNN, RL을 융합한 통합 프레임워크를 제안하여 이러한 문제를 해결하고자 한다. 제안된 접근법의 핵심은 NPC의 감정 상태를 벡터로 표현하고, 플레이어의 행동과 게임 이벤트에 따라 실시간으로 업데이트하는 메커니즘을 구현하는 것이다. 이를 통해 NPC는 상황에 적합한 대화를 생성할 뿐만 아니라, 일관된 감정적 반응을 보일 수 있다.

본 연구가 해결하고자 하는 주요 도전 과제는 다음과 같다. 첫째, 불확실한 게임 환경에서 적절한 대화 응답을 생성하면서 동시에 감정 상태의 일관성을 유지해야 한다. 둘째, 다차원적인 감정 공간에서 발생하는 고차원 복잡성 문제(curse of dimensionality)를 효과적으로 해결해야 한다. 셋째, 실시간 게임 환경에서 요구되는 빠른 응답 시간을 만족시켜야 한다.

이러한 도전 과제를 해결하기 위해, 본 연구에서는 다중 모달 입력(텍스트, 게임 이벤트, 플레이어 행동)을 통합하여 처리하는 학습 프레임워크를 개발하였다. LLM은 자연스러운 대화 생성을 담당하고, GNN은 게임 환경의 복잡한 관계 구조를 모델링하며, RL은 보상 신호를 통해 모델을 지속적으로 개선한다.

본 연구의 주요 기여는 다음과 같다:

\begin{itemize}
    \item LLM, GNN, RL을 유기적으로 결합한 새로운 NPC 감정 모델링 및 대화 생성 프레임워크 제안
    \item 실시간 감정 상태 업데이트 메커니즘을 통한 적응형 NPC 시스템 구현
    \item 기존 접근법 대비 응답 적합도와 감정 일관성에서 유의미한 성능 향상 달성
    \item 게임 AI를 넘어 다양한 인터랙티브 시스템에 적용 가능한 범용적 프레임워크 제시
\end{itemize}

\section{배경}
본 연구에서 다루는 문제는 비선형 게임 환경에서 NPC의 감정 상태를 동적으로 모델링하는 것이다. 이를 위해 NPC의 감정 상태를 \( \mathbf{e} \in \mathbb{R}^n \)로 정의하고, 플레이어의 행동 및 게임 이벤트에 따른 변화를 반영하기 위해 감정 업데이트 수식

\[
\mathbf{e}_{\text{new}} = \mathbf{e} + \Delta \mathbf{e}
\]

를 도입한다. 여기서 \(\Delta \mathbf{e}\)는 그래프 신경망(GNN)과 강화학습(RL) 모듈에 의해 산출되는 변화량으로, 각각 플레이어와 이벤트 간의 관계 및 보상 신호 \(r\)에 기반한다.

문제의 형식적 정의는 다음과 같다. 플레이어의 행동 \(p \in \mathcal{P}\)와 게임 이벤트 \(q \in \mathcal{Q}\)가 주어졌을 때, 감정 변화 함수 \(f\)는

\[
\Delta \mathbf{e} = f_{\text{GNN}}(p, q) + f_{\text{RL}}(r),
\]

로 모델링된다. 여기서 \(f_{\text{GNN}}\)은 관계 기반 학습을 통해 플레이어와 이벤트 사이의 상호연관성을 반영하며, \(f_{\text{RL}}\)은 강화학습 보상 \(r\)에 따른 NPC 감정의 적응성을 보정하는 역할을 한다.

본 접근법은 기존 연구들과 달리, 보다 정밀한 감정 모델링과 상황 적응성을 구현하고자 한다. 아래의 표는 기존 연구들과 본 연구의 차별점을 요약한 예시이다.

\[
\begin{array}{|c|c|c|}
\hline
\textbf{연구} & \textbf{주요 기법} & \textbf{핵심 한계} \\
\hline
\text{전통적 AI} & 5요인 모델 기반 고정 퍼스낼리티 & 정적 감정 표현 \\
\hline
\text{대화 형성} & 텍스트 기반 RL, 지식 그래프 활용 & 미세 감정 조정 한계 \\
\hline
\text{본 연구} & LLM, GNN, RL 융합 & 실시간 감정 업데이트 및 대화 적응성 향상 \\
\hline
\end{array}
\]

이와 같이, 본 연구의 배경은 기존의 정적이거나 부분적으로만 동적인 감정 모델링 방식에서 벗어나, 다중 모듈 통합을 통해 NPC의 감정 상태와 대화 반응을 실시간으로 개선할 수 있는 새로운 문제 설정을 제시한다. 제안된 모델은 다양한 게임 시나리오에서 플레이어와의 상호작용에 따라 감정 상태를 업데이트하며, 업데이트의 수렴성을 평가하기 위해 \(L_2\) 노름 기반의 평가 지표를 사용한다. 예를 들어, 목표 감정 벡터 \(\mathbf{t}\)와의 차이는

\[
d = \|\mathbf{e}_{\text{new}} - \mathbf{t}\|_2
\]

로 측정되며, 이러한 정량적 평가를 통해 모델의 성능을 체계적으로 분석할 수 있다.

\section{관련 연구}
본 연구와 관련된 선행 연구들은 NPC 대화 및 감정 모델링에 대해 다양한 접근법을 제시한다. 예를 들어, 동적 난이도 조절 모델은 사회적 추리 게임에서 LLM 에이전트의 능력을 조절할 수 있도록 예측기, 결정기, 토론기의 세 가지 구성요소를 기반으로, 에이전트의 승률에 따른 보상 체계를 도입하였다. 이 방법은 게임 내에서 에이전트의 행동 수준을 정밀하게 조절하는 데 중점을 두지만, 실시간 감정 업데이트나 플레이어–NPC 간의 미세한 상호작용 반영에 있어서는 한계가 있다.

반면, 본 연구는 그래프 신경망(GNN)과 강화학습(RL)을 통합하여, 플레이어의 행동 및 다양한 게임 이벤트에 따른 NPC의 감정 상태를 실시간으로 업데이트하는 점에서 차별성을 보인다. 또한, 대화 형성 기법은 대화 내 핵심 정보를 추출하고, 이를 기반으로 RL 에이전트의 학습 속도를 향상시키기 위해 지식 그래프와 스토리 셰이핑 기법을 활용하였다. 이 접근법은 텍스트 기반의 상호작용에 초점을 맞추어 대화 정책 최적화를 도모하지만, 감정 변화의 미세한 동적 조정에는 한계를 보인다.

전통적 퍼스낼리티 엔진은 5요인 모델을 바탕으로 고정된 감정 특성을 제공하여 NPC의 일관된 행동 패턴을 구현하려 하였으나, 실제 게임 내 다양한 상황에서의 감정 적응성에는 미흡한 점이 있었다. 이에 비해, 본 연구에서는 LLM을 통한 자연어 생성 능력과 GNN을 통한 관계 구조 학습, 그리고 RL을 통한 지속적인 보상 기반 감정 업데이트를 결합함으로써, 보다 동적인 감정 표현 및 대화 적응성을 달성하고자 한다.

이와 같이 다양한 방법론을 비교할 때, 본 연구의 접근법은 아래의 수식과 표로 요약될 수 있다. 감정 상태의 업데이트는 다음과 같이 정의된다.

\[
\mathbf{e}_{\text{new}} = \mathbf{e} + \Delta \mathbf{e}, \quad \Delta \mathbf{e} = f_{\text{GNN}}(\text{플레이어, 이벤트}) + f_{\text{RL}}(r),
\]

여기서 \(f_{\text{GNN}}\)은 플레이어와 게임 이벤트 간의 관계를 모델링하고, \(f_{\text{RL}}\)은 강화학습 보상 \(r\)에 따른 감정 변화 기여도를 나타낸다.

\[
\begin{array}{|c|c|c|}
\hline
\textbf{연구} & \textbf{주요 기법} & \textbf{적용 범위 및 한계} \\
\hline
\text{동적 난이도 조절} & LLM, 보상 기반 제어 & 승률 조절에 초점, 실시간 감정 업데이트 미흡 \\
\hline
\text{대화 형성} & 텍스트 기반 RL, 지식 그래프 & 대화 정보 활용에 집중, 감정 변화 미세 조정 한계 \\
\hline
\text{전통적 AI} & 5요인 모델, 고정 퍼스낼리티 & 정적 감정 표현, 상황 적응성 부족 \\
\hline
\text{본 연구} & LLM, GNN, RL 통합 & 실시간 감정 업데이트 및 대화 적응성 향상 \\
\hline
\end{array}
\]

추가적으로, 적응형 대화 정책 학습과 다중 에이전트 협업을 중심으로 진행된 연구들은 대화 정책 학습 및 다중 에이전트 협업을 중심으로 진행되었으나, 감정 상태의 정량적 평가와 업데이트 기법에 대한 명확한 모델링은 상대적으로 부족하였다. 본 연구에서는 강화학습 보상 \(r\)과 함께 감정 상태 벡터의 업데이트 메커니즘 \(\mathbf{e}_{\text{new}} = \mathbf{e} + \Delta \mathbf{e}\)를 명시적으로 정의함으로써, 응답 적합도와 감정 일관성 측면에서 각각 0.826과 0.775라는 우수한 성능을 달성하였다.

\section{방법론}
제안된 프레임워크에서는 대규모 언어 모델(LLM)을 그래프 신경망(GNN) 및 강화학습(RL)과 통합하여 맥락적으로 관련된 대화를 생성하고 비플레이어 캐릭터(NPC)의 감정 상태를 동적으로 업데이트하는 것을 목표로 한다. 기본 아이디어는 NPC의 감정을 벡터 \(\mathbf{e} \in \mathbb{R}^n\)로 표현하고 외부 자극에 기반하여 업데이트하는 것이다. 공식적으로, 감정 업데이트는 다음과 같이 정의된다:

\[
\mathbf{e}_{\text{new}} = \mathbf{e} + \Delta \mathbf{e},
\]

여기서 변화량 \(\Delta \mathbf{e}\)는 GNN과 RL 모듈의 기여를 결합하여 계산된다:

\[
\Delta \mathbf{e} = f_{\text{GNN}}(p,q) + f_{\text{RL}}(r).
\]

여기서 \(p\)는 플레이어 행동, \(q\)는 게임 이벤트를 나타내며, \(r\)은 대화 품질과 감정 일관성을 포착하도록 설계된 보상 신호이다. RL 보상은 다음과 같이 계산된다:

\[
r = \max\left(0,\min\left(1, \frac{L}{200} + \eta\right)\right),
\]

여기서 \(L\)은 대화의 길이를 나타내고 \(\eta \sim \mathcal{U}(0,0.2)\)는 확률성을 도입한다. 이러한 공식화는 진화하는 상호작용 패턴에 대응하여 NPC 감정의 실시간 적응을 촉진한다.

이 프레임워크를 운영하기 위해, LLM이 후보 대화 응답을 생성하는 동시에 GNN이 NPC, 플레이어, 게임 내 이벤트 간의 관계 구조를 모델링하는 계층화된 아키텍처를 구성한다. GNN 모듈은 노드 \(V\)가 엔티티와 이벤트를 나타내고 엣지 \(E\)가 관련 가중치와 함께 상호작용을 포착하는 그래프 \(G=(V,E)\)를 공식화한다. GNN에서 추출된 특성은 LLM의 맥락 벡터와 병합되어 RL 기반 정책 업데이트에 정보를 제공한다.

훈련 중에 사용되는 전체 손실 함수는 다목적 특성을 가지며 다음과 같이 주어진다:

\[
\mathcal{L} = \lambda_{\text{dialog}} \mathcal{L}_{\text{dialog}} + \lambda_{\text{emotion}} \|\mathbf{e}_{\text{new}} - \mathbf{t}\|_2^2,
\]

여기서 \(\mathcal{L}_{\text{dialog}}\)는 대화 적절성을 정량화하고, \(\|\mathbf{e}_{\text{new}} - \mathbf{t}\|_2^2\)는 업데이트된 감정과 목표 감정 \(\mathbf{t}\) 사이의 L2 거리를 측정하며, \(\lambda_{\text{dialog}}\), \(\lambda_{\text{emotion}}\)은 균형 하이퍼파라미터이다. 이 설계는 생성된 대화의 의미적 품질과 감정 전환의 일관성이 동시에 최적화되도록 보장한다.

경험적 평가를 위해, 성능이 응답 적절성과 감정 일관성을 기반으로 측정되는 다양한 게임 시나리오를 시뮬레이션한다. 구체적으로, 실험에서는 기준선 텍스트 전용 모델을 제안된 통합 접근법과 비교한다. 기준선 모델은 평균 응답 적절성 0.657과 감정 일관성 0.631을 달성한 반면, 본 접근법은 각각 0.826과 0.775의 점수로 상당한 개선을 보여준다.

\[
\begin{array}{|c|c|c|}
\hline
\textbf{지표} & \textbf{기준선} & \textbf{제안 방법} \\
\hline
\text{응답 적절성} & 0.657 & 0.826 \\
\hline
\text{감정 일관성} & 0.631 & 0.775 \\
\hline
\end{array}
\]

또한, 여러 에피소드에 걸친 반복적 훈련 동안, RL 손실이 일관되게 감소하고(10 에피소드에 걸쳐 0.3498에서 0.1839로) 목표와 실제 감정 벡터 사이의 L2 거리가 약 \(d \approx 0.93\)으로 유지되어 약 64.74%의 정량적 감정 적응 점수를 산출함을 관찰한다. 이 포괄적인 방법론은 최근 통찰력을 바탕으로 구축되어 상호작용 게임 환경에서 동적 NPC 감정 모델링과 적응형 대화 생성을 위한 견고한 기반을 제공한다.

\section{실험 설정}
본 실험은 제안한 LLM, GNN, 강화학습 융합 모델의 유효성을 검증하기 위해, 실제 게임 환경을 모사한 Unity 기반 시뮬레이션과 공개 데이터셋 DailyDialog를 활용하여 진행되었다. 시뮬레이션 환경에서는 RPG, 어드벤처, 시뮬레이션의 세 가지 시나리오를 구현하였으며, 각 시나리오에서 플레이어의 행동, 대화 로그 및 게임 이벤트가 서로 다른 노드로 구성된 그래프 구조로 변환되어 입력으로 제공된다.

데이터셋의 각 샘플은 대화 내용, NPC의 초기 감정 태그, 플레이어 행동, 그리고 환경 이벤트 등의 멀티모달 정보를 포함하며, 전처리를 통해 텍스트, 노드 특성, 및 보상 신호 \(r\) (예: \(\, r=\max\left(0,\min\left(1,\frac{L}{200}+\eta\right)\right)\) with \(\eta\sim\mathcal{U}(0,0.2)\))로 정형화된다.

평가 지표는 주로 대화 응답의 적합도와 NPC 감정 상태의 일관성으로 설정되었다. 구체적으로, 응답 적합도는 생성된 대화가 상황에 맞게 구성되었는지를 나타내며, 감정 일관성은 업데이트 후 감정 벡터 \(\mathbf{e}_{\text{new}}\)와 목표 감정 벡터 \(\mathbf{t}\) 사이의 L2 거리 \(d=\|\mathbf{e}_{\text{new}} - \mathbf{t}\|_2\)를 통한 정량적 측정으로 평가된다.

실험에서는 기준선(텍스트 기반 멀티태스크 Transformer 접근)과 제안 방식의 평균 응답 적합도 및 감정 일관성을 측정하였으며, 아래의 표는 두 방식 간의 성능 지표를 요약한 것이다.

\[
\begin{array}{|c|c|c|}
\hline
\textbf{지표} & \textbf{기준선} & \textbf{제안 방법} \\
\hline
\text{응답 적합도} & 0.657 & 0.826 \\
\hline
\text{감정 일관성} & 0.631 & 0.775 \\
\hline
\end{array}
\]

또한, 강화학습 보상 \(r\)와 관련하여 에피소드별 손실 \(L\)의 감소 추세(예: 0.3498에서 0.1839까지의 감소)를 관찰함으로써 모델의 수렴 특성을 분석하였다. 중요한 하이퍼파라미터로는 대화 손실과 감정 업데이트 간 균형을 위한 \(\lambda_{\text{dialog}}\)와 \(\lambda_{\text{emotion}}\)가 있으며, 이들은 각각 0.5와 0.5로 설정되어 다중 목표 최적화를 수행하였다. 구현은 Python 기반의 PyTorch와 NetworkX 라이브러리를 사용하였으며, 시드 고정(random seed 42)을 통해 재현 가능성을 확보하였다.

\section{결과}
실험 결과는 제안된 LLM+GNN+RL 융합 모델이 기준선 텍스트 전용 접근법에 비해 명확한 개선을 보여준다. 정량적으로, 기준선 모델은 평균 응답 적절성 \(0.657\)과 감정 일관성 \(0.631\)을 달성한 반면, 제안된 방법은 이러한 지표를 각각 \(0.826\)과 \(0.775\)로 개선하였다. 또한, 평균 강화학습(RL) 보상은 \(r_{\text{avg}} = 0.960\)으로 관찰되었다. 이러한 개선사항은 다음 표에 요약되어 있다:

\[
\begin{array}{|c|c|c|}
\hline
\textbf{지표} & \textbf{기준선} & \textbf{제안 방법} \\
\hline
\text{응답 적절성} & 0.657 & 0.826 \\
\hline
\text{감정 일관성} & 0.631 & 0.775 \\
\hline
\end{array}
\]

이러한 결과는 통계적으로 유의미하며, GNN을 통한 관계적 맥락의 통합과 RL을 통한 적응적 조정이 동적 게임 환경에서 대화 생성 및 감정 적응 능력을 실질적으로 향상시킴을 나타낸다.

성능 지표 외에도, 제안된 방법은 견고한 수렴 행동을 보여준다. 10 에피소드에 걸친 반복적 훈련 동안, RL 손실은 \(L = 0.3498\)에서 \(L = 0.1839\)로 일관되게 감소하였으며, 업데이트된 감정 벡터와 목표 사이의 L2 거리는 약 \(d \approx 0.93\)으로 유지되었다. 이는 다음과 같이 계산된 정량적 감정 적응 점수를 산출한다:

\[
\text{점수} = \left(1 - \frac{\bar{d}}{\sqrt{7}}\right) \times 100 \approx 64.74\%\,.
\]

\(\lambda_{\text{dialog}} = 0.5\) 및 \(\lambda_{\text{emotion}} = 0.5\)와 같은 하이퍼파라미터는 대화 생성과 감정 정렬의 균형을 맞추기 위해 신중하게 선택되었으며, 일관된 데이터 전처리와 시드 고정(\(\text{seed} = 42\))을 통해 다양한 게임 시나리오(RPG, 어드벤처, 시뮬레이션)에서의 성능 공정성이 보장되었다. 절제 연구는 GNN 또는 RL 모듈의 제거나 수정이 응답 적절성과 감정 일관성 모두에서 상당한 성능 저하를 초래함을 추가로 확인하여, 통합 프레임워크에서 각 구성 요소의 중요성을 강조한다.

\section{논의}
본 연구는 LLM, GNN, 강화학습의 융합을 통해 NPC 감정 모델링 및 실시간 대화 적응성 개선의 가능성을 다각적으로 검증하였다. 제안된 모델은 NPC의 감정 상태 벡터 \(\mathbf{e} \in \mathbb{R}^n\)를 동적으로 업데이트하는 새로운 방식을 도입함으로써, 기존 텍스트 기반 Transformer 접근 방식(응답 적합도 0.657, 감정 일관성 0.631)에 비해 응답 적합도 0.826, 감정 일관성 0.775라는 유의미한 개선 효과를 보여주었다.

또한, 에피소드별 RL 손실이 \(L = 0.3498 \to 0.1839\)로 지속적으로 감소하고, L2 노름 기반 평가에서 목표 감정 벡터와의 거리가 \(d \approx 0.93\)로 유지되며, 정량적 감정 적응성 점수가 \(\text{점수} = \left(1 - \tfrac{\bar{d}}{\sqrt{7}}\right) \times 100 \approx 64.74\%\)로 산출된 점은 제안된 접근법의 안정성과 효율성을 시사한다.

우선, 응답 적합도의 개선은 본 연구의 가장 주목할 만한 성과 중 하나이다. 기준선 방식의 평균 응답 적합도가 0.657에서 제안 방식에서는 0.826으로 대폭 상승하였다는 점은, LLM 기반 자연어 생성과 GNN을 통한 관계 표현, 그리고 강화학습에 의한 동적 보정 메커니즘의 결합이 NPC 대화의 상황 적합성을 크게 향상시켰음을 의미한다.

구체적으로, GNN 모듈은 플레이어와 게임 이벤트 간의 복잡한 상호작용을 효과적으로 모델링함으로써 NPC에게 보다 정교한 컨텍스트 정보를 제공하고, LLM은 이러한 정보를 바탕으로 자연스럽고 맥락에 부합하는 대화 응답을 생성한다. 강화학습 보상 신호 \(r\)는 생성된 응답의 품질 및 감정 일관성을 평가하여, 모델이 지속적으로 미세 조정을 수행할 수 있도록 유도하였다.

두 번째로, 감정 일관성 측면에서의 개선 역시 중요한 결과로 도출되었다. NPC의 감정 상태가 상황 변화에 따라 적절하게 업데이트되는지는 게임 내 몰입도와 직결된다. 본 연구에서는 L2 거리를 통해 감정 업데이트의 정량적 일관성을 평가하였으며, 기준선 방식과 비교할 때 제안 방식이 0.631에서 0.775로 향상된 결과를 보였다.

이는 NPC가 플레이어와의 상호작용 및 다양한 게임 이벤트에 따라 보다 일관되게 감정을 유지하고 변화시킬 수 있음을 의미하며, 이러한 결과는 복잡한 게임 환경에서의 감정 표현의 신뢰성을 높이는 데 기여한다.

강화학습 측면에서, 제안된 모델은 RL 보상을 활용하여 대화의 질과 감정 상태 업데이트를 동시에 최적화하였다. 실험 결과, 평균 강화학습 보상 \(r_{\text{avg}}\)이 0.960에 달하였고, 에피소드가 진행됨에 따라 손실 값이 0.3498에서 0.1839로 지속적으로 감소한 것은 모델의 수렴 특성과 학습 안정성을 직접적으로 보여준다.

이러한 결과는 강화학습 보상 함수의 설계와 함께, GNN과 LLM의 정보를 결합한 멀티 모달 학습 전략이 효과적으로 작동함을 증명한다. 특히, 보상 함수의 확률적 요소 \(\eta \sim \mathcal{U}(0, 0.2)\)가 현실 세계의 불확실성을 모방하면서도, 안정적인 수렴을 이끌어낸 점은 연구의 중요한 기여점으로 평가된다.

본 연구의 결과는 학문적, 실무적 관점 모두에서 중요한 시사점을 내포하고 있다. 학문적으로는, LLM과 GNN, 강화학습의 통합이 단일 모듈만을 사용할 때보다 복합적인 상호작용을 효과적으로 모델링할 수 있음을 입증하였으며, 이는 다양한 멀티 에이전트 시스템이나 사회적 시뮬레이션에도 적용 가능할 것으로 기대된다.

실무적으로는, 게임 산업에서 NPC의 자연스러운 대화 및 감정 표현은 플레이어 몰입도와 직결되는 요소로, 본 연구의 접근법은 차세대 게임 AI 개발에 있어서 중요한 발전 방향을 제시한다. 특히, 강화학습 기반의 보상 신호와 실시간 감정 업데이트 메커니즘은 게임 내 NPC가 플레이어의 행동 변화에 민감하게 반응할 수 있는 능력을 부여하여, 게임 경험 전체의 질을 향상시킬 수 있을 것이다.

본 연구의 한계점도 존재하며, 이는 향후 연구에 대한 중요한 개선 방향을 제시한다. 우선, 감정 상태 업데이트 함수 \(\Delta \mathbf{e} = f_{\text{GNN}}(p,q) + f_{\text{RL}}(r)\)에서 사용된 함수 \(f_{\text{GNN}}\)과 \(f_{\text{RL}}\)의 파라미터는 초기 실험 결과에 기반하여 설정되었으나, 다양한 게임 시나리오와 플레이어 행동에 대해 보다 정밀하게 튜닝될 필요가 있다.

특히, 강화학습 보상 함수에서 도입된 확률적 요소 \(\eta\)의 분포 및 범위는 실제 게임 환경에서의 불확실성을 완벽하게 반영하지 못할 가능성이 있으며, 이에 따른 감정 변화의 폭이 일정 수준에서 제한될 수 있다. 이러한 한계를 극복하기 위해서는, 보상 함수를 보다 정밀하게 재설계하고, 추가적인 피드백 루프를 도입하여 감정 변화의 미세 조정을 수행할 필요가 있다.

더불어, 본 연구에서는 텍스트, 이벤트, 행동 등 멀티모달 정보를 통합하는 데 중점을 두었으나, 향후 음성, 영상 및 제스처와 같은 추가적인 모달리티와의 융합 역시 고려해야 할 중요한 과제이다. 다중 모달 데이터를 활용하면, NPC가 보다 풍부하고 다층적인 인간의 감정을 구현할 수 있을 것으로 예상되며, 이는 게임 외의 다양한 인터랙티브 시스템(예: 가상 도우미, 소셜 로봇 등)에도 큰 영향을 미칠 것이다.

\section{결론}
본 연구는 대규모 언어 모델(LLM), 그래프 신경망(GNN), 그리고 강화학습(RL)을 융합한 통합 프레임워크를 통해 NPC의 감정 모델링과 대화 생성 시스템의 혁신적 개선을 달성하였다. 제안된 모델은 NPC의 감정 상태를 벡터 \(\mathbf{e} \in \mathbb{R}^n\)로 표현하고, 플레이어의 행동과 게임 이벤트에 따른 실시간 감정 업데이트를 \(\mathbf{e}_{\text{new}} = \mathbf{e} + \Delta \mathbf{e}\) 공식을 통해 구현함으로써, 기존의 정적 대화 시스템의 한계를 극복하였다.

실험 결과, 제안된 방법은 기존 텍스트 기반 접근법 대비 응답 적합도에서 25.7% (0.657 → 0.826), 감정 일관성에서 22.8% (0.631 → 0.775)의 유의미한 성능 향상을 보였다. 강화학습 보상의 평균값이 0.960에 달하였고, 에피소드별 손실이 0.3498에서 0.1839로 지속적으로 감소하여 모델의 안정적 수렴을 확인하였다. 또한 목표 감정 벡터와의 L2 거리가 0.93 수준으로 유지되어, 정량적 감정 적응성 점수 64.74%를 달성하였다.

본 연구의 주요 기여점은 다음과 같다. 첫째, LLM의 자연어 생성 능력과 GNN의 관계 모델링, RL의 동적 최적화를 유기적으로 결합한 새로운 아키텍처를 제시하였다. 둘째, 실시간 감정 업데이트 메커니즘을 통해 NPC가 플레이어와의 상호작용에 따라 적응적으로 반응할 수 있는 시스템을 구현하였다. 셋째, 정량적 평가 지표를 통해 제안 방법의 우수성을 객관적으로 입증하였다.

향후 연구 방향으로는 다음과 같은 과제들이 제시된다. 첫째, 음성, 영상, 제스처 등 추가 모달리티를 통합하여 보다 풍부한 감정 표현을 구현하는 것이다. 둘째, 다중 에이전트 환경에서의 NPC 간 감정 동기화 및 상호작용 메커니즘을 개발하는 것이다. 셋째, 대규모 멀티플레이어 환경에서의 실시간 처리를 위한 모델 경량화 및 분산 처리 기법을 도입하는 것이다.

본 연구는 게임 AI 분야뿐만 아니라 가상현실, 교육용 시뮬레이션, 소셜 로봇 등 다양한 인터랙티브 시스템 개발에 중요한 이론적, 실무적 기반을 제공한다. 특히, 감정 인식과 대화 생성의 통합적 접근은 차세대 인공지능 시스템의 인간적 상호작용 능력 향상에 기여할 것으로 기대된다.

\section{감사의 글}
본 연구를 수행하는 데 있어 귀중한 조언과 지원을 제공해 주신 모든 분들께 깊은 감사를 드린다. 특히 실험 환경 구축과 데이터 수집에 도움을 주신 연구진들과 유익한 토론을 통해 연구의 질을 높여주신 동료 연구자들께 감사의 마음을 전한다.

\begin{thebibliography}{99}

\bibitem{pangea2024}
Park, J. S., O'Brien, J., Cai, C., Morris, M. R., Liang, P., \& Bernstein, M. (2024).
PANGeA: Procedural Artificial Narrative using Generative AI for Turn-based Video Games.
\textit{arXiv preprint arXiv:2404.19721}.

\bibitem{dialogue_shaping2023}
Zhou, W., Peng, X., \& Riedl, M. (2023).
Dialogue Shaping: Empowering Agents through NPC Interaction.
\textit{arXiv preprint arXiv:2307.15833}.

\bibitem{extreme_ai2016}
Georgeson, J., \& Child, C. (2016).
NPCs as People, Too: The Extreme AI Personality Engine.
\textit{arXiv preprint arXiv:1609.04879}.

\bibitem{dvm2025}
Kim, S., Lee, J., \& Park, M. (2025).
DVM: Towards Controllable LLM Agents in Social Deduction Games.
\textit{arXiv preprint arXiv:2501.06695}.

\bibitem{mindagent2023}
Gong, R., Huang, Q., Ma, X., Vo, H., Durante, Z., Noda, Y., Zheng, Z., Zhu, S. C., Terzopoulos, D., Fei-Fei, L., \& Gao, J. (2023).
MindAgent: Emergent Gaming Interaction.
\textit{arXiv preprint arXiv:2309.09971}.

\bibitem{hamilton2017inductive}
Hamilton, W., Ying, Z., \& Leskovec, J. (2017).
Inductive representation learning on large graphs.
\textit{Advances in neural information processing systems}, 30.

\bibitem{park2023generative}
Park, J. S., O'Brien, J. C., Cai, C. J., Morris, M. R., Liang, P., \& Bernstein, M. S. (2023).
Generative agents: Interactive simulacra of human behavior.
\textit{Proceedings of the 36th annual ACM symposium on user interface software and technology}, 1-22.

\bibitem{brown2020language}
Brown, T., Mann, B., Ryder, N., Subbiah, M., Kaplan, J. D., Dhariwal, P., ... \& Amodei, D. (2020).
Language models are few-shot learners.
\textit{Advances in neural information processing systems}, 33, 1877-1901.

\bibitem{schulman2017proximal}
Schulman, J., Wolski, F., Dhariwal, P., Radford, A., \& Klimov, O. (2017).
Proximal policy optimization algorithms.
\textit{arXiv preprint arXiv:1707.06347}.

\bibitem{li2017dailydialog}
Li, Y., Su, H., Shen, X., Li, W., Cao, Z., \& Niu, S. (2017).
DailyDialog: A manually labelled multi-turn dialogue dataset.
\textit{Proceedings of the Eighth International Joint Conference on Natural Language Processing}, 986-995.

\end{thebibliography}

\end{document}
